package com.drex.customer.api.request;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 客户绑定查询参数
 * 
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerBindQuery implements Serializable {
    
    /**
     * 社交平台类型（必填）
     */
    private String socialPlatform;
    
    /**
     * 社交平台用户ID
     */
    private String socialUserId;
    
    /**
     * 社交平台用户名
     */
    private String socialUserName;
    
    /**
     * 社交平台邮箱（主要用于Google平台）
     */
    private String socialEmail;
    
    /**
     * 客户ID
     */
    private String passportId;
}